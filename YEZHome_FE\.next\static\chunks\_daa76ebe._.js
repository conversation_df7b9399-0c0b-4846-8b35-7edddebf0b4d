(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/actions/server/data:12e015 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40b8bf52df741401c7c2f61d096cc243772a94f063":"checkFavoriteStatus"},"app/actions/server/userFavorite.jsx",""] */ __turbopack_context__.s({
    "checkFavoriteStatus": (()=>checkFavoriteStatus)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var checkFavoriteStatus = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("40b8bf52df741401c7c2f61d096cc243772a94f063", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "checkFavoriteStatus"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/actions/server/data:909a9e [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"40f2b1915562886d43a901d95ff8c50055a5956498":"addToFavorites"},"app/actions/server/userFavorite.jsx",""] */ __turbopack_context__.s({
    "addToFavorites": (()=>addToFavorites)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var addToFavorites = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("40f2b1915562886d43a901d95ff8c50055a5956498", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "addToFavorites"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/actions/server/data:25f0b0 [app-client] (ecmascript) <text/javascript>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ [{"403c24c3bd50d955eb88cdadad68d9b1fe0078c414":"removeFromFavorites"},"app/actions/server/userFavorite.jsx",""] */ __turbopack_context__.s({
    "removeFromFavorites": (()=>removeFromFavorites)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js [app-client] (ecmascript)");
"use turbopack no side effects";
;
var removeFromFavorites = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createServerReference"])("403c24c3bd50d955eb88cdadad68d9b1fe0078c414", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["callServer"], void 0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$client$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findSourceMapURL"], "removeFromFavorites"); //# sourceMappingURL=data:application/json;base64,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
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>PropertyDetailClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-client] (ecmascript) <export default as ChevronLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Heart$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/heart.js [app-client] (ecmascript) <export default as Heart>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$share$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Share$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/share.js [app-client] (ecmascript) <export default as Share>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-client] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$send$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Send$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/send.js [app-client] (ecmascript) <export default as Send>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/ui/button.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/shared/lib/app-dynamic.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$toast$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/hooks/use-toast.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$contexts$2f$AuthContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/contexts/AuthContext.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$909a9e__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/actions/server/data:909a9e [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$25f0b0__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/actions/server/data:25f0b0 [app-client] (ecmascript) <text/javascript>");
;
;
;
;
;
;
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
// Dynamically import components
const NearbyPropertiesCarousel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/property/NearbyPropertiesCarousel.jsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/components/property/NearbyPropertiesCarousel.jsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    loading: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "h-16 bg-white animate-pulse"
        }, void 0, false, {
            fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
            lineNumber: 16,
            columnNumber: 18
        }, this)
});
_c = NearbyPropertiesCarousel;
const PropertyImageGallery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/property/PropertyImageGallery.jsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/components/property/PropertyImageGallery.jsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    loading: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "h-64 bg-gray-100 animate-pulse rounded-md"
        }, void 0, false, {
            fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
            lineNumber: 20,
            columnNumber: 18
        }, this)
});
_c1 = PropertyImageGallery;
const PropertyDescription = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/property/PropertyDescription.jsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/components/property/PropertyDescription.jsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    loading: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "h-64 bg-white animate-pulse"
        }, void 0, false, {
            fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
            lineNumber: 24,
            columnNumber: 18
        }, this)
});
_c2 = PropertyDescription;
const PropertyContactForm = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/property/PropertyContactForm.jsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/components/property/PropertyContactForm.jsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    ssr: false
});
_c3 = PropertyContactForm;
const DetailMap = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/property/DetailMap.jsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/components/property/DetailMap.jsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    ssr: false,
    loading: ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "h-64 bg-gray-100 animate-pulse rounded-md"
        }, void 0, false, {
            fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
            lineNumber: 33,
            columnNumber: 18
        }, this)
});
_c4 = DetailMap;
const ShareModal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$shared$2f$lib$2f$app$2d$dynamic$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>__turbopack_context__.r("[project]/components/user-property/ShareModal.jsx [app-client] (ecmascript, next/dynamic entry, async loader)")(__turbopack_context__.i), {
    loadableGenerated: {
        modules: [
            "[project]/components/user-property/ShareModal.jsx [app-client] (ecmascript, next/dynamic entry)"
        ]
    },
    ssr: false
});
_c5 = ShareModal;
function PropertyDetailClient({ property, isFavorite = false, onToggleFavorite }) {
    _s();
    const [isContactModalOpen, setIsContactModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isShareModalOpen, setIsShareModalOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [favorite, setFavorite] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(isFavorite);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])("Common");
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])("PropertyCard");
    const { toast } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$toast$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"])();
    const { isLoggedIn } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$contexts$2f$AuthContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    // Update local favorite state when prop changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PropertyDetailClient.useEffect": ()=>{
            setFavorite(isFavorite);
        }
    }["PropertyDetailClient.useEffect"], [
        isFavorite
    ]);
    const handleFavoriteClick = async (e)=>{
        e.stopPropagation();
        if (!isLoggedIn) {
            if (onToggleFavorite) {
                onToggleFavorite(property.id, false);
            }
            return;
        }
        setIsLoading(true);
        try {
            const newFavoriteStatus = !favorite;
            const result = newFavoriteStatus ? await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$909a9e__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["addToFavorites"])(property.id) : await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$25f0b0__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["removeFromFavorites"])(property.id);
            if (result.success) {
                setFavorite(newFavoriteStatus);
                if (onToggleFavorite) {
                    onToggleFavorite(property.id, newFavoriteStatus);
                }
                toast({
                    title: newFavoriteStatus ? t("addedToFavorites") : t("removedFromFavorites"),
                    description: newFavoriteStatus ? t("addedToFavoritesDesc") : t("removedFromFavoritesDesc"),
                    variant: "default"
                });
            } else {
                toast({
                    title: t("errorOccurred"),
                    description: result.message || t("cannotUpdateFavorite"),
                    variant: "destructive"
                });
            }
        } catch (error) {
            console.error("Error toggling favorite:", error);
            toast({
                title: t("errorOccurred"),
                description: t("cannotUpdateFavorite"),
                variant: "destructive"
            });
        } finally{
            setIsLoading(false);
        }
    };
    // Handle back button click
    const handleBackClick = ()=>{
        router.back();
    };
    const handleContactModalClose = ()=>{
        setIsContactModalOpen(false);
    };
    const handleShareClick = ()=>{
        setIsShareModalOpen(true);
    };
    const handleShareModalClose = ()=>{
        setIsShareModalOpen(false);
    };
    // Process address data
    let formattedAddress = property.address || "";
    let center = {
        latitude: property.latitude,
        longitude: property.longitude
    };
    try {
        if (property.placeData) {
            const placeData = JSON.parse(property.placeData);
            if (placeData.result && placeData.result.formatted_address) {
                formattedAddress = placeData.result.formatted_address;
            }
        }
    } catch (e) {
        console.error("Error parsing placeData:", e);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white mx-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                        className: "sticky top-0 z-10 bg-white p-4 border-b flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleBackClick,
                                className: "flex items-center text-gray-600",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__["ChevronLeft"], {
                                        className: "h-5 w-5 mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                        lineNumber: 151,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: "Quay lại"
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                        lineNumber: 152,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                lineNumber: 150,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-1 flex justify-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    src: "/yezhome_logo.png",
                                    alt: "YEZ Home",
                                    width: 120,
                                    height: 120
                                }, void 0, false, {
                                    fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                    lineNumber: 155,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                lineNumber: 154,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        className: `flex items-center gap-1 ${favorite ? 'text-coral-500' : 'text-gray-700'} transition-colors duration-300`,
                                        onClick: handleFavoriteClick,
                                        disabled: isLoading,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$heart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Heart$3e$__["Heart"], {
                                                className: `h-5 w-5 ${isLoading ? 'animate-pulse' : ''}`,
                                                fill: favorite ? "currentColor" : "none"
                                            }, void 0, false, {
                                                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                                lineNumber: 163,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "hidden sm:inline",
                                                children: "Lưu"
                                            }, void 0, false, {
                                                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                                lineNumber: 164,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                        lineNumber: 158,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        className: "flex items-center gap-1 text-gray-700 hover:text-teal-600 transition-colors",
                                        onClick: handleShareClick,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$share$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Share$3e$__["Share"], {
                                                className: "h-5 w-5"
                                            }, void 0, false, {
                                                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                                lineNumber: 170,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "hidden sm:inline",
                                                children: "Chia sẻ"
                                            }, void 0, false, {
                                                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                                lineNumber: 171,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                        lineNumber: 166,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: handleBackClick,
                                        className: "flex items-center gap-1 text-gray-700",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                                className: "h-5 w-5"
                                            }, void 0, false, {
                                                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                                lineNumber: 174,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "hidden sm:inline",
                                                children: "Đóng"
                                            }, void 0, false, {
                                                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                                lineNumber: 175,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                        lineNumber: 173,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                lineNumber: 157,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                        lineNumber: 149,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PropertyImageGallery, {
                            images: property.propertyMedia?.map((pm)=>pm.mediaURL) || [],
                            propertyName: property.name
                        }, void 0, false, {
                            fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                            lineNumber: 182,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                        lineNumber: 181,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-4 grid grid-cols-1 lg:grid-cols-4 gap-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "lg:col-span-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PropertyDescription, {
                                        property: {
                                            ...property,
                                            address: formattedAddress,
                                            propertyType: property.propertyType ? tCommon(`propertyType_${property.propertyType}`) : "",
                                            postType: property.postType ? tCommon(`propertyPostType_${property.postType}`) : "",
                                            direction: property.direction ? tCommon(`${property.direction}`) : "__",
                                            balconyDirection: property.balconyDirection ? tCommon(`${property.balconyDirection}`) : "__",
                                            legality: property.legality ? tCommon(`legality_${property.legality}`) : "__",
                                            interior: property.interior ? tCommon(`interior_${property.interior}`) : "__"
                                        }
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                        lineNumber: 191,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                className: "text-2xl font-bold mb-4",
                                                children: "Vị trí bất động sản"
                                            }, void 0, false, {
                                                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                                lineNumber: 209,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(DetailMap, {
                                                property: property,
                                                center: center
                                            }, void 0, false, {
                                                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                                lineNumber: 210,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                        lineNumber: 208,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-4",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "border-t pt-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                                    className: "text-2xl font-bold mb-4",
                                                    children: "Bất động sản lân cận"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                                    lineNumber: 216,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(NearbyPropertiesCarousel, {
                                                    latitude: property.latitude,
                                                    longitude: property.longitude,
                                                    tCommon: tCommon
                                                }, void 0, false, {
                                                    fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                                    lineNumber: 217,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                            lineNumber: 215,
                                            columnNumber: 15
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                        lineNumber: 214,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                lineNumber: 190,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "lg:col-span-1",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white border rounded-md p-4 sticky top-20",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$ui$2f$button$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                                        className: "w-full bg-teal-600 text-white hover:bg-teal-700 font-semibold py-7 px-4 rounded-md mb-3 text-lg",
                                        onClick: ()=>setIsContactModalOpen(true),
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$send$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Send$3e$__["Send"], {}, void 0, false, {
                                                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                                lineNumber: 233,
                                                columnNumber: 17
                                            }, this),
                                            " Liên hệ người bán"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                        lineNumber: 229,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                    lineNumber: 228,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                                lineNumber: 227,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                        lineNumber: 189,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                lineNumber: 147,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(PropertyContactForm, {
                isOpen: isContactModalOpen,
                onClose: handleContactModalClose,
                propertyId: property.id,
                ownerId: property.ownerId
            }, void 0, false, {
                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                lineNumber: 241,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ShareModal, {
                open: isShareModalOpen,
                onClose: handleShareModalClose,
                property: property
            }, void 0, false, {
                fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx",
                lineNumber: 249,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(PropertyDetailClient, "YAp1BfqhI4arADAN8HbI/I/jqUE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$hooks$2f$use$2d$toast$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useToast"],
        __TURBOPACK__imported__module__$5b$project$5d2f$contexts$2f$AuthContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c6 = PropertyDetailClient;
var _c, _c1, _c2, _c3, _c4, _c5, _c6;
__turbopack_context__.k.register(_c, "NearbyPropertiesCarousel");
__turbopack_context__.k.register(_c1, "PropertyImageGallery");
__turbopack_context__.k.register(_c2, "PropertyDescription");
__turbopack_context__.k.register(_c3, "PropertyContactForm");
__turbopack_context__.k.register(_c4, "DetailMap");
__turbopack_context__.k.register(_c5, "ShareModal");
__turbopack_context__.k.register(_c6, "PropertyDetailClient");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/[locale]/bds/[id]/PropertyDetailWrapper.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>PropertyDetailWrapper)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$12e015__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__ = __turbopack_context__.i("[project]/app/actions/server/data:12e015 [app-client] (ecmascript) <text/javascript>");
var __TURBOPACK__imported__module__$5b$project$5d2f$contexts$2f$AuthContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/contexts/AuthContext.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$locale$5d2f$bds$2f5b$id$5d2f$PropertyDetailClient$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/[locale]/bds/[id]/PropertyDetailClient.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
function PropertyDetailWrapper({ property }) {
    _s();
    const [isFavorite, setIsFavorite] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { isLoggedIn } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$contexts$2f$AuthContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    // Check favorite status when component mounts and user is logged in
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PropertyDetailWrapper.useEffect": ()=>{
            const fetchFavoriteStatus = {
                "PropertyDetailWrapper.useEffect.fetchFavoriteStatus": async ()=>{
                    if (!isLoggedIn || !property?.id) return;
                    try {
                        const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$server$2f$data$3a$12e015__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$text$2f$javascript$3e$__["checkFavoriteStatus"])([
                            property.id
                        ]);
                        if (result.success && result.data.length > 0) {
                            setIsFavorite(result.data[0].isFavorite);
                        }
                    } catch (error) {
                        console.error("Error fetching favorite status:", error);
                    }
                }
            }["PropertyDetailWrapper.useEffect.fetchFavoriteStatus"];
            if (isLoggedIn) {
                fetchFavoriteStatus();
            }
        }
    }["PropertyDetailWrapper.useEffect"], [
        property?.id,
        isLoggedIn
    ]);
    // Handle toggling favorite status
    const handleToggleFavorite = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "PropertyDetailWrapper.useCallback[handleToggleFavorite]": (propertyId, newIsFavorite)=>{
            setIsFavorite(newIsFavorite);
            // Dispatch a custom event that the navbar can listen to
            window.dispatchEvent(new CustomEvent("favorites-changed"));
        }
    }["PropertyDetailWrapper.useCallback[handleToggleFavorite]"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f5b$locale$5d2f$bds$2f5b$id$5d2f$PropertyDetailClient$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        property: property,
        isFavorite: isFavorite,
        onToggleFavorite: handleToggleFavorite
    }, void 0, false, {
        fileName: "[project]/app/[locale]/bds/[id]/PropertyDetailWrapper.tsx",
        lineNumber: 46,
        columnNumber: 5
    }, this);
}
_s(PropertyDetailWrapper, "5jRAby8A7MabLEunW199BxCyc8Q=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$contexts$2f$AuthContext$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = PropertyDetailWrapper;
var _c;
__turbopack_context__.k.register(_c, "PropertyDetailWrapper");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/node_modules/next/dist/shared/lib/image-external.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
0 && (module.exports = {
    default: null,
    getImageProps: null
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
_export(exports, {
    default: function() {
        return _default;
    },
    getImageProps: function() {
        return getImageProps;
    }
});
const _interop_require_default = __turbopack_context__.r("[project]/node_modules/@swc/helpers/cjs/_interop_require_default.cjs [app-client] (ecmascript)");
const _getimgprops = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/get-img-props.js [app-client] (ecmascript)");
const _imagecomponent = __turbopack_context__.r("[project]/node_modules/next/dist/client/image-component.js [app-client] (ecmascript)");
const _imageloader = /*#__PURE__*/ _interop_require_default._(__turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/image-loader.js [app-client] (ecmascript)"));
function getImageProps(imgProps) {
    const { props } = (0, _getimgprops.getImgProps)(imgProps, {
        defaultLoader: _imageloader.default,
        // This is replaced by webpack define plugin
        imgConf: ("TURBOPACK compile-time value", JSON.parse('{"deviceSizes":[640,750,828,1080,1200,1920,2048,3840],"imageSizes":[16,32,48,64,96,128,256,384],"path":"/_next/image","loader":"default","dangerouslyAllowSVG":false,"unoptimized":false,"domains":[],"remotePatterns":[{"hostname":"localhost"},{"hostname":"tiles.goong.io"},{"hostname":"images.unsplash.com"},{"hostname":"plus.unsplash.com"},{"hostname":"example.com"}]}'))
    });
    // Normally we don't care about undefined props because we pass to JSX,
    // but this exported function could be used by the end user for anything
    // so we delete undefined props to clean it up a little.
    for (const [key, value] of Object.entries(props)){
        if (value === undefined) {
            delete props[key];
        }
    }
    return {
        props
    };
}
const _default = _imagecomponent.Image; //# sourceMappingURL=image-external.js.map
}}),
"[project]/node_modules/next/image.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/image-external.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>ChevronLeft)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "m15 18-6-6 6-6",
            key: "1wnfg3"
        }
    ]
];
const ChevronLeft = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("ChevronLeft", __iconNode);
;
 //# sourceMappingURL=chevron-left.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-client] (ecmascript) <export default as ChevronLeft>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChevronLeft": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/share.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Share)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",
            key: "1b2hhj"
        }
    ],
    [
        "polyline",
        {
            points: "16 6 12 2 8 6",
            key: "m901s6"
        }
    ],
    [
        "line",
        {
            x1: "12",
            x2: "12",
            y1: "2",
            y2: "15",
            key: "1p0rca"
        }
    ]
];
const Share = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("Share", __iconNode);
;
 //# sourceMappingURL=share.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/share.js [app-client] (ecmascript) <export default as Share>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Share": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$share$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$share$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/share.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/send.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.473.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Send)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-client] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",
            key: "1ffxy3"
        }
    ],
    [
        "path",
        {
            d: "m21.854 2.147-10.94 10.939",
            key: "12cjpa"
        }
    ]
];
const Send = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("Send", __iconNode);
;
 //# sourceMappingURL=send.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/send.js [app-client] (ecmascript) <export default as Send>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Send": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$send$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$send$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/send.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "BailoutToCSR", {
    enumerable: true,
    get: function() {
        return BailoutToCSR;
    }
});
const _bailouttocsr = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/lazy-dynamic/bailout-to-csr.js [app-client] (ecmascript)");
function BailoutToCSR(param) {
    let { reason, children } = param;
    if (typeof window === 'undefined') {
        throw Object.defineProperty(new _bailouttocsr.BailoutToCSRError(reason), "__NEXT_ERROR_CODE", {
            value: "E394",
            enumerable: false,
            configurable: true
        });
    }
    return children;
} //# sourceMappingURL=dynamic-bailout-to-csr.js.map
}}),
"[project]/node_modules/next/dist/shared/lib/encode-uri-path.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "encodeURIPath", {
    enumerable: true,
    get: function() {
        return encodeURIPath;
    }
});
function encodeURIPath(file) {
    return file.split('/').map((p)=>encodeURIComponent(p)).join('/');
} //# sourceMappingURL=encode-uri-path.js.map
}}),
"[project]/node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
'use client';
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "PreloadChunks", {
    enumerable: true,
    get: function() {
        return PreloadChunks;
    }
});
const _jsxruntime = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const _reactdom = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react-dom/index.js [app-client] (ecmascript)");
const _workasyncstorageexternal = __turbopack_context__.r("[project]/node_modules/next/dist/server/app-render/work-async-storage.external.js [app-client] (ecmascript)");
const _encodeuripath = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/encode-uri-path.js [app-client] (ecmascript)");
function PreloadChunks(param) {
    let { moduleIds } = param;
    // Early return in client compilation and only load requestStore on server side
    if (typeof window !== 'undefined') {
        return null;
    }
    const workStore = _workasyncstorageexternal.workAsyncStorage.getStore();
    if (workStore === undefined) {
        return null;
    }
    const allFiles = [];
    // Search the current dynamic call unique key id in react loadable manifest,
    // and find the corresponding CSS files to preload
    if (workStore.reactLoadableManifest && moduleIds) {
        const manifest = workStore.reactLoadableManifest;
        for (const key of moduleIds){
            if (!manifest[key]) continue;
            const chunks = manifest[key].files;
            allFiles.push(...chunks);
        }
    }
    if (allFiles.length === 0) {
        return null;
    }
    const dplId = ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : '';
    return /*#__PURE__*/ (0, _jsxruntime.jsx)(_jsxruntime.Fragment, {
        children: allFiles.map((chunk)=>{
            const href = workStore.assetPrefix + "/_next/" + (0, _encodeuripath.encodeURIPath)(chunk) + dplId;
            const isCss = chunk.endsWith('.css');
            // If it's stylesheet we use `precedence` o help hoist with React Float.
            // For stylesheets we actually need to render the CSS because nothing else is going to do it so it needs to be part of the component tree.
            // The `preload` for stylesheet is not optional.
            if (isCss) {
                return /*#__PURE__*/ (0, _jsxruntime.jsx)("link", {
                    // @ts-ignore
                    precedence: "dynamic",
                    href: href,
                    rel: "stylesheet",
                    as: "style"
                }, chunk);
            } else {
                // If it's script we use ReactDOM.preload to preload the resources
                (0, _reactdom.preload)(href, {
                    as: 'script',
                    fetchPriority: 'low'
                });
                return null;
            }
        })
    });
} //# sourceMappingURL=preload-chunks.js.map
}}),
"[project]/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
const _jsxruntime = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/jsx-runtime.js [app-client] (ecmascript)");
const _react = __turbopack_context__.r("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
const _dynamicbailouttocsr = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js [app-client] (ecmascript)");
const _preloadchunks = __turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/lazy-dynamic/preload-chunks.js [app-client] (ecmascript)");
// Normalize loader to return the module as form { default: Component } for `React.lazy`.
// Also for backward compatible since next/dynamic allows to resolve a component directly with loader
// Client component reference proxy need to be converted to a module.
function convertModule(mod) {
    // Check "default" prop before accessing it, as it could be client reference proxy that could break it reference.
    // Cases:
    // mod: { default: Component }
    // mod: Component
    // mod: { default: proxy(Component) }
    // mod: proxy(Component)
    const hasDefault = mod && 'default' in mod;
    return {
        default: hasDefault ? mod.default : mod
    };
}
const defaultOptions = {
    loader: ()=>Promise.resolve(convertModule(()=>null)),
    loading: null,
    ssr: true
};
function Loadable(options) {
    const opts = {
        ...defaultOptions,
        ...options
    };
    const Lazy = /*#__PURE__*/ (0, _react.lazy)(()=>opts.loader().then(convertModule));
    const Loading = opts.loading;
    function LoadableComponent(props) {
        const fallbackElement = Loading ? /*#__PURE__*/ (0, _jsxruntime.jsx)(Loading, {
            isLoading: true,
            pastDelay: true,
            error: null
        }) : null;
        // If it's non-SSR or provided a loading component, wrap it in a suspense boundary
        const hasSuspenseBoundary = !opts.ssr || !!opts.loading;
        const Wrap = hasSuspenseBoundary ? _react.Suspense : _react.Fragment;
        const wrapProps = hasSuspenseBoundary ? {
            fallback: fallbackElement
        } : {};
        const children = opts.ssr ? /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {
            children: [
                typeof window === 'undefined' ? /*#__PURE__*/ (0, _jsxruntime.jsx)(_preloadchunks.PreloadChunks, {
                    moduleIds: opts.modules
                }) : null,
                /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {
                    ...props
                })
            ]
        }) : /*#__PURE__*/ (0, _jsxruntime.jsx)(_dynamicbailouttocsr.BailoutToCSR, {
            reason: "next/dynamic",
            children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Lazy, {
                ...props
            })
        });
        return /*#__PURE__*/ (0, _jsxruntime.jsx)(Wrap, {
            ...wrapProps,
            children: children
        });
    }
    LoadableComponent.displayName = 'LoadableComponent';
    return LoadableComponent;
}
const _default = Loadable; //# sourceMappingURL=loadable.js.map
}}),
"[project]/node_modules/next/dist/shared/lib/app-dynamic.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "default", {
    enumerable: true,
    get: function() {
        return dynamic;
    }
});
const _interop_require_default = __turbopack_context__.r("[project]/node_modules/@swc/helpers/cjs/_interop_require_default.cjs [app-client] (ecmascript)");
const _loadable = /*#__PURE__*/ _interop_require_default._(__turbopack_context__.r("[project]/node_modules/next/dist/shared/lib/lazy-dynamic/loadable.js [app-client] (ecmascript)"));
function dynamic(dynamicOptions, options) {
    var _mergedOptions_loadableGenerated;
    const loadableOptions = {};
    if (typeof dynamicOptions === 'function') {
        loadableOptions.loader = dynamicOptions;
    }
    const mergedOptions = {
        ...loadableOptions,
        ...options
    };
    return (0, _loadable.default)({
        ...mergedOptions,
        modules: (_mergedOptions_loadableGenerated = mergedOptions.loadableGenerated) == null ? void 0 : _mergedOptions_loadableGenerated.modules
    });
}
if ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {
    Object.defineProperty(exports.default, '__esModule', {
        value: true
    });
    Object.assign(exports.default, exports);
    module.exports = exports.default;
} //# sourceMappingURL=app-dynamic.js.map
}}),
}]);

//# sourceMappingURL=_daa76ebe._.js.map